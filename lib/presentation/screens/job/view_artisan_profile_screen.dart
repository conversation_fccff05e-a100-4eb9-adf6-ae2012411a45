import 'package:build_mate/data/dto/responses_dto/artisan_profile_data_response.dart';
import 'package:build_mate/presentation/components/dismissible_image_viewer.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/view_models/artisan/artisan_profile_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';

class ViewArtisanProfileScreen extends ConsumerStatefulWidget {
  const ViewArtisanProfileScreen({super.key});

  @override
  ConsumerState<ViewArtisanProfileScreen> createState() =>
      _ViewArtisanProfileScreenState();
}

class _ViewArtisanProfileScreenState
    extends ConsumerState<ViewArtisanProfileScreen> {
  @override
  Widget build(BuildContext context) {
    ref.read(artisanProfileViewModelProvider.notifier);
    final state = ref.watch(artisanProfileViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body:
          state.isLoading
              ? _buildSkeletonLoader(context)
              : SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Cover Image and Profile Section
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        // Cover Image with loading indicator
                        Stack(
                          children: [
                            Image.network(
                              state.artisanProfileDataResponse?.coverPhoto ??
                                  '',
                              height: 200,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              loadingBuilder: (
                                context,
                                child,
                                loadingProgress,
                              ) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  height: 200,
                                  width: double.infinity,
                                  color: Colors.grey[200],
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      value:
                                          loadingProgress.expectedTotalBytes !=
                                                  null
                                              ? loadingProgress
                                                      .cumulativeBytesLoaded /
                                                  loadingProgress
                                                      .expectedTotalBytes!
                                              : null,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.teal[700]!,
                                      ),
                                    ),
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  height: 200,
                                  width: double.infinity,
                                  color: Colors.grey[200],
                                  child: const Center(
                                    child: Icon(
                                      Icons.image_not_supported,
                                      size: 40,
                                      color: Colors.grey,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),

                        // Contact Actions
                        Positioned(
                          top: 40,
                          right: 16,
                          child: Row(
                            children: [
                              _buildIconButton(Icons.edit_note_outlined),
                            ],
                          ),
                        ),

                        // Profile Image
                        Positioned(
                          bottom: -40,
                          left: 16,
                          child: CircleAvatar(
                            radius: 40,
                            backgroundColor: Colors.grey[200],
                            child:
                                state.artisanProfileDataResponse?.avatar == null
                                    ? Icon(
                                      Icons.person,
                                      size: 40,
                                      color: Colors.grey[400],
                                    )
                                    : ClipOval(
                                      child: Image.network(
                                        state
                                                .artisanProfileDataResponse
                                                ?.avatar ??
                                            '',
                                        width: 80,
                                        height: 80,
                                        fit: BoxFit.cover,
                                        loadingBuilder: (
                                          context,
                                          child,
                                          loadingProgress,
                                        ) {
                                          if (loadingProgress == null) {
                                            return child;
                                          }
                                          return Center(
                                            child: CircularProgressIndicator(
                                              value:
                                                  loadingProgress
                                                              .expectedTotalBytes !=
                                                          null
                                                      ? loadingProgress
                                                              .cumulativeBytesLoaded /
                                                          loadingProgress
                                                              .expectedTotalBytes!
                                                      : null,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                    Colors.teal[700]!,
                                                  ),
                                              strokeWidth: 2,
                                            ),
                                          );
                                        },
                                        errorBuilder: (
                                          context,
                                          error,
                                          stackTrace,
                                        ) {
                                          return Icon(
                                            Icons.person,
                                            size: 40,
                                            color: Colors.grey[400],
                                          );
                                        },
                                      ),
                                    ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 48),

                    // Profile Info
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                state.artisanProfileDataResponse?.name ?? '',
                                style: MyTypography.SemiBold.copyWith(
                                  fontSize: 24,
                                  color: customColors.textPrimaryColor,
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Padding(
                                padding: EdgeInsets.only(right: 4),
                                child: Icon(
                                  Icons.verified,
                                  size: 20,
                                  color: Colors.blue,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),

                          // Rating
                          Row(
                            children: [
                              const Icon(
                                Icons.star,
                                color: Colors.amber,
                                size: 20,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${state.rating} (${state.totalReviews} Reviews)',
                                style: MyTypography.Regular.copyWith(
                                  color: customColors.textPrimaryColor
                                      .withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Divider
                          Divider(
                            color: customColors.textPrimaryColor.withValues(
                              alpha: 0.1,
                            ),
                            thickness: 1,
                          ),

                          const SizedBox(height: 16),

                          // Service Type Card
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color:
                                  themeMode == ThemeMode.light
                                      ? darkBlueColor
                                      : customColors.primaryContainer,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.work,
                                  color:
                                      themeMode == ThemeMode.light
                                          ? Colors.white
                                          : customColors.textPrimaryColor,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  state
                                          .artisanProfileDataResponse
                                          ?.specializations
                                          ?.first
                                          .services
                                          ?.name ??
                                      '',
                                  style: MyTypography.SemiBold.copyWith(
                                    color:
                                        themeMode == ThemeMode.light
                                            ? Colors.white
                                            : customColors.textPrimaryColor,
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Tabs
                          _buildTabs(),

                          const SizedBox(height: 24),

                          // Tab Content
                          _buildTabContent(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildIconButton(IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: const BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
      ),
      child: Icon(icon, color: Colors.black87),
    );
  }

  Widget _buildTabs() {
    return Row(
      children: [
        _buildTab('Overview', 0),
        _buildTab('Recent Work', 1),
        _buildTab('Reviews', 2),
      ],
    );
  }

  Widget _buildTab(String text, int index) {
    final state = ref.watch(artisanProfileViewModelProvider);
    final viewModel = ref.read(artisanProfileViewModelProvider.notifier);

    final isSelected = state.selectedTabIndex == index;
    return GestureDetector(
      onTap: () {
        viewModel.setSelectedTabIndex(index);
      },
      child: Container(
        margin: const EdgeInsets.only(right: 24),
        padding: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? Colors.orange : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          text,
          style: MyTypography.Medium.copyWith(
            color: isSelected ? Colors.orange : Colors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow:
            themeMode == ThemeMode.light
                ? [
                  BoxShadow(
                    color: Colors.black.withAlpha((0.05 * 255).round()),
                    blurRadius: 10,
                  ),
                ]
                : null,
        border:
            themeMode == ThemeMode.dark
                ? Border(
                  top: BorderSide(
                    color: customColors.textPrimaryColor.withValues(alpha: 0.1),
                    width: 1,
                  ),
                )
                : null,
      ),
      child: SafeArea(
        child: PrimaryButton(
          text: 'Contact',
          onPressed: () async {},
          height: 48.0,
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    final state = ref.watch(artisanProfileViewModelProvider);

    switch (state.selectedTabIndex) {
      case 0:
        return _buildOverviewTab();
      case 1:
        return _buildRecentWorkTab();
      case 2:
        return _buildReviewsTab();
      default:
        return _buildOverviewTab();
    }
  }

  Widget _buildOverviewTab() {
    final state = ref.watch(artisanProfileViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Specialization
        Text(
          'Specialisation',
          style: MyTypography.SemiBold.copyWith(
            fontSize: 18,
            color: customColors.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              (state
                          .artisanProfileDataResponse
                          ?.specializations
                          ?.first
                          .specializationTags ??
                      [])
                  .map<Widget>(
                    (spec) => Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.grey[200]
                                : customColors.surfaceVariant,
                        borderRadius: BorderRadius.circular(20),
                        border:
                            themeMode == ThemeMode.dark
                                ? Border.all(
                                  color: customColors.textPrimaryColor
                                      .withValues(alpha: 0.1),
                                  width: 1,
                                )
                                : null,
                      ),
                      child: Text(
                        spec.subCategories?.name ?? '',
                        style: MyTypography.Regular.copyWith(
                          color: customColors.textPrimaryColor,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),

        const SizedBox(height: 24),

        // About
        Text(
          'About',
          style: MyTypography.SemiBold.copyWith(
            fontSize: 18,
            color: customColors.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          state.artisanProfileDataResponse?.about ?? '',
          style: MyTypography.Regular.copyWith(
            color: customColors.textPrimaryColor.withValues(alpha: 0.7),
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentWorkTab() {
    final state = ref.watch(artisanProfileViewModelProvider);
    final artisanImages = state.artisanProfileDataResponse?.artisanImages ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 4),

        // Show empty state if no images
        artisanImages.isEmpty
            ? Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 40),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.photo_library_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No recent work images available',
                      style: MyTypography.Medium.copyWith(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            )
            : GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              itemCount: artisanImages.length,
              itemBuilder: (context, index) {
                final imageUrl = artisanImages[index].imageUrl ?? '';
                return GestureDetector(
                  onTap:
                      () => _showImageViewer(
                        context,
                        artisanImages.map((img) => img.imageUrl ?? '').toList(),
                        index,
                      ),
                  child: Hero(
                    tag: 'work_image_$index',
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value:
                                  loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.teal[700]!,
                              ),
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[200],
                            child: Icon(
                              Icons.broken_image,
                              color: Colors.grey[400],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
      ],
    );
  }

  Widget _buildReviewsTab() {
    final state = ref.watch(artisanProfileViewModelProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Client Reviews',
              style: MyTypography.SemiBold.copyWith(fontSize: 18),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.amber.shade100,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  const Icon(Icons.star, color: Colors.amber, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    '${state.artisanProfileDataResponse?.artisanRatings?.isNotEmpty == true ? _calculateAverageRating(state.artisanProfileDataResponse!.artisanRatings!) : 0.0}',
                    style: MyTypography.SemiBold.copyWith(
                      color: Colors.amber.shade800,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount:
              state.artisanProfileDataResponse?.artisanRatings?.length ?? 0,
          separatorBuilder:
              (context, index) =>
                  Divider(color: Colors.grey[200], thickness: 1, height: 32),
          itemBuilder: (context, index) {
            final review =
                state.artisanProfileDataResponse?.artisanRatings?[index];
            return _buildReviewItem(review);
          },
        ),
      ],
    );
  }

  Widget _buildReviewItem(ArtisanRatingData? review) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: Colors.grey[200],
              child: ClipOval(
                child: Image.network(
                  review?.client?.avatar ?? '',
                  width: 40,
                  height: 40,
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Center(
                      child: CircularProgressIndicator(
                        value:
                            loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                                : null,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.teal[700]!,
                        ),
                        strokeWidth: 2,
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.person,
                      size: 20,
                      color: Colors.grey[400],
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(review?.client?.name ?? '', style: MyTypography.SemiBold),
                const SizedBox(height: 4),
                Row(
                  children: [
                    ...List.generate(
                      5,
                      (index) => Icon(
                        index < (review?.rating ?? 0)
                            ? Icons.star
                            : Icons.star_border,
                        color: Colors.amber,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatTimeAgo(review?.createdAt ?? ''),
                      style: MyTypography.Regular.copyWith(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          review?.comments ?? '',
          style: MyTypography.Regular.copyWith(
            color: Colors.grey[700],
            height: 1.4,
          ),
        ),
      ],
    );
  }

  void _showImageViewer(
    BuildContext context,
    List<String> images,
    int initialIndex,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => DismissibleImageViewer(
            images: images,
            initialIndex: initialIndex,
          ),
    );
  }

  Widget _buildSkeletonLoader(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      period: const Duration(milliseconds: 1500), // Add animation period
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cover image placeholder
            Container(height: 200, width: double.infinity, color: Colors.white),

            // Profile image placeholder with positioning
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Transform.translate(
                offset: const Offset(0, -40),
                child: CircleAvatar(radius: 40, backgroundColor: Colors.white),
              ),
            ),

            const SizedBox(height: 8),

            // Profile info placeholders
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(width: 200, height: 24, color: Colors.white),
                  const SizedBox(height: 8),
                  Container(width: 120, height: 16, color: Colors.white),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    height: 1,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    height: 50,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Container(width: 80, height: 20, color: Colors.white),
                      const SizedBox(width: 24),
                      Container(width: 80, height: 20, color: Colors.white),
                      const SizedBox(width: 24),
                      Container(width: 80, height: 20, color: Colors.white),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Container(width: 150, height: 20, color: Colors.white),
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    height: 100,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimeAgo(String dateString) {
    if (dateString.isEmpty) return '';

    try {
      final DateTime date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 365) {
        final years = (difference.inDays / 365).floor();
        return '$years ${years == 1 ? 'year' : 'years'} ago';
      } else if (difference.inDays > 30) {
        final months = (difference.inDays / 30).floor();
        return '$months ${months == 1 ? 'month' : 'months'} ago';
      } else if (difference.inDays > 7) {
        final weeks = (difference.inDays / 7).floor();
        return '$weeks ${weeks == 1 ? 'week' : 'weeks'} ago';
      } else if (difference.inDays > 0) {
        return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return dateString;
    }
  }

  double _calculateAverageRating(List<ArtisanRatingData> ratings) {
    if (ratings.isEmpty) return 0.0;

    double totalRating = 0.0;
    int validRatings = 0;

    for (var rating in ratings) {
      if (rating.rating != null) {
        totalRating += rating.rating!;
        validRatings++;
      }
    }

    if (validRatings == 0) return 0.0;
    return double.parse((totalRating / validRatings).toStringAsFixed(1));
  }
}
