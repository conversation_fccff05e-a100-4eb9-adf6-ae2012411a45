// ignore_for_file: unused_import

import 'package:build_mate/presentation/components/helper_widgets/spacing_widgets.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/screens/services/service_categories_screen.dart';
import 'package:build_mate/presentation/view_models/job/post_job_view_model.dart';
import 'package:build_mate/presentation/state/post_job_flow_state.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'dart:io';

class PostJobScreen extends ConsumerStatefulWidget {
  const PostJobScreen({super.key});

  @override
  ConsumerState<PostJobScreen> createState() => _PostJobScreenState();
}

class _PostJobScreenState extends ConsumerState<PostJobScreen> {
  late PostJobViewModel viewModel;
  final int maxImages = 4;

  @override
  void initState() {
    super.initState();

    // Initialize the viewModel
    viewModel = ref.read(postJobViewModelProvider.notifier);

    // Add this to ensure the view model is updated when the screen is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (kDebugMode) {
        print(
          'PostJobScreen: refreshWithSelectedService called from initState',
        );
        final state = ref.read(postJobViewModelProvider);
        print(
          'Current state: ${state.selectedMainCategory}, ${state.selectedMainCategoryId}',
        );
      }
      viewModel.refreshWithSelectedService();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Get the current state
    final state = ref.read(postJobViewModelProvider);

    if (kDebugMode) {
      print('PostJobScreen didChangeDependencies called');
      print(
        'Current state: ${state.selectedMainCategory}, ${state.selectedMainCategoryId}',
      );
    }

    // If we have a selected service ID but no subcategories, refresh them
    if (state.selectedMainCategoryId > 0 &&
        state.selectedMainCategory.isNotEmpty &&
        state.relatedSubCategories.isEmpty) {
      if (kDebugMode) {
        print('Refreshing subcategories for ${state.selectedMainCategory}');
      }
      ref
          .read(postJobViewModelProvider.notifier)
          .loadSubcategoriesForService(state.selectedMainCategory);
    }
  }

  Widget _buildImageGrid() {
    final state = ref.watch(postJobViewModelProvider);

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount:
          state.jobImageFiles.length < maxImages
              ? state.jobImageFiles.length + 1
              : maxImages,
      itemBuilder: (context, index) {
        if (index == state.jobImageFiles.length && index < maxImages) {
          return _buildAddImageButton(index);
        }
        return _buildImageTile(index, state.jobImageFiles[index]);
      },
    );
  }

  Widget _buildAddImageButton(int index) {
    return InkWell(
      onTap: () {
        debugPrint('Add image button tapped at index: $index');
        viewModel.pickImage(index);
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 32,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 8),
            Text(
              'Add Image',
              style: MyTypography.Medium.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageTile(int index, File imageFile) {
    final isUploading = viewModel.isImageUploading(index);
    final progress = viewModel.getImageUploadProgress(index);
    final error = viewModel.getImageUploadError(index);

    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
              image: FileImage(imageFile),
              fit: BoxFit.cover,
              // Dim the image if it's uploading or has an error
              colorFilter:
                  (isUploading || error != null)
                      ? ColorFilter.mode(
                        Colors.black.withAlpha((0.3 * 255).round()),
                        BlendMode.darken,
                      )
                      : null,
            ),
          ),
        ),

        // Upload progress indicator
        if (isUploading)
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    value: progress,
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${(progress * 100).toInt()}%',
                  style: MyTypography.Medium.copyWith(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

        // Error indicator
        if (error != null)
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 32),
                const SizedBox(height: 8),
                Text(
                  'Upload failed',
                  style: MyTypography.Medium.copyWith(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                ElevatedButton(
                  onPressed: () => viewModel.retryImageUpload(index),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    minimumSize: const Size(80, 30),
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                  child: Text(
                    'Retry',
                    style: MyTypography.Medium.copyWith(
                      color: Colors.black87,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

        // Remove button
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () {
              viewModel.removeImage(index);
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.black54,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, size: 16, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySection() {
    final state = ref.watch(postJobViewModelProvider);
    final viewModel = ref.read(postJobViewModelProvider.notifier);

    if (kDebugMode) {
      print('Building category section with state:');
      print('  Selected main category: ${state.selectedMainCategory}');
      print('  Selected main category ID: ${state.selectedMainCategoryId}');
      print('  Related subcategories: ${state.relatedSubCategories}');
      print('  Selected subcategories: ${state.selectedSubCategories}');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Category',
          style: MyTypography.SemiBold.copyWith(fontSize: 16),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () async {
            // Navigate to service categories screen and await result
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ServiceCategoriesScreen(),
              ),
            );

            // Check if result contains category data
            if (result != null && result is Map<String, dynamic>) {
              if (kDebugMode) {
                print('Received result from ServiceCategoriesScreen: $result');
              }

              // Update the view model with the selected category
              viewModel.onCategorySelected(
                result['mainCategory'],
                result['subCategory'],
                List<String>.from(result['subCategories']),
              );
            }

            // Refresh the view model to ensure data is up to date
            if (kDebugMode) {
              print('Returned from ServiceCategoriesScreen, refreshing data');
            }
            viewModel.refreshWithSelectedService();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey.withAlpha((0.3 * 255).round()),
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (state.selectedMainCategory.isNotEmpty) ...[
                        Text(
                          state.selectedMainCategory,
                          style: MyTypography.Regular.copyWith(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 4),
                      ],
                      Text(
                        state.selectedSubCategories.isNotEmpty
                            ? state
                                .selectedSubCategories
                                .first // Show first selected subcategory
                            : 'Select Service',
                        style: MyTypography.Regular.copyWith(
                          color:
                              state.selectedSubCategories.isNotEmpty
                                  ? Colors.black87
                                  : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
        ),
        if (state.selectedMainCategory.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            'Related Services',
            style: MyTypography.SemiBold.copyWith(fontSize: 16),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                state.relatedSubCategories.map((subCategory) {
                  final isSelected = state.selectedSubCategories.contains(
                    subCategory,
                  );
                  return FilterChip(
                    label: Text(subCategory),
                    selected: isSelected,
                    onSelected: (bool selected) {
                      if (selected) {
                        viewModel.addSubCategory(subCategory);
                      } else {
                        viewModel.removeSubCategory(subCategory);
                      }
                    },
                    selectedColor: orangeColor.withAlpha((0.2 * 255).round()),
                    checkmarkColor: orangeColor,
                    labelStyle: MyTypography.Medium.copyWith(
                      color: isSelected ? orangeColor : Colors.black87,
                    ),
                  );
                }).toList(),
          ),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(postJobViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Theme-aware colors for app bar
    final appBarColor =
        themeMode == ThemeMode.light
            ? darkBlueColor
            : customColors.surfaceVariant;
    final textColor =
        themeMode == ThemeMode.light
            ? Colors.white
            : customColors.textPrimaryColor;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: appBarColor,
        title: Text(
          'Post a Job',
          style: MyTypography.SemiBold.copyWith(color: textColor, fontSize: 20),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: textColor),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category Search
                  _buildCategorySection(),
                  const SizedBox(height: 24),

                  // Date Picker
                  Text(
                    'Select Date',
                    style: MyTypography.SemiBold.copyWith(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: state.serviceDate ?? DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (picked != null) {
                        viewModel.updateServiceDate(picked);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            state.serviceDate != null
                                ? DateFormat(
                                  'MMM dd, yyyy',
                                ).format(state.serviceDate!)
                                : 'Select a date',
                            style: MyTypography.Medium,
                          ),
                          const Icon(Icons.calendar_today, size: 20),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Budget TextField
                  Text(
                    'Your estimated budget (optional)',
                    style: MyTypography.SemiBold.copyWith(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: viewModel.budgetController,
                    keyboardType: TextInputType.number,
                    onChanged: (value) => viewModel.updateBudget(value),
                    decoration: InputDecoration(
                      hintText: 'Enter your budget',
                      prefixIcon: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Center(
                          widthFactor: 0,
                          child: Text(
                            'USD',
                            style: MyTypography.Medium.copyWith(
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Description TextField
                  Text(
                    'Job Description',
                    style: MyTypography.SemiBold.copyWith(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: viewModel.descriptionController,
                    onChanged: (value) => viewModel.updateDescription(value),
                    maxLines: 5,
                    decoration: InputDecoration(
                      hintText: 'Describe the job in detail...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  Text(
                    'Add Images',
                    style: MyTypography.SemiBold.copyWith(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  _buildImageGrid(),
                  vSpace(16),

                  // Post Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed:
                          state.isSubmitting
                              ? null
                              : () async {
                                try {
                                  // Call the postJob method and check the result
                                  final success = await viewModel.postJob(
                                    context,
                                  );

                                  if (success && context.mounted) {
                                    // Show success dialog only if posting was successful
                                    _showSuccessDialog(context);
                                  }
                                } catch (e) {
                                  // Error is already handled in viewModel.postJob
                                  if (kDebugMode) {
                                    print('Error in post job button: $e');
                                  }
                                }
                              },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: orangeColor,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child:
                          state.isSubmitting
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                              : Text(
                                'Post Job',
                                style: MyTypography.SemiBold.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (state.isLoading)
            Container(
              color: Colors.black.withAlpha((0.3 * 255).round()),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha((0.1 * 255).round()),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Success icon/image
                Image.asset(
                  'assets/images/successful.png',
                  height: 100,
                  width: 100,
                ),
                const SizedBox(height: 24),

                // Success title
                Text(
                  'Job Posted Successfully!',
                  style: MyTypography.SemiBold.copyWith(
                    fontSize: 20,
                    color: darkBlueColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // Success message
                Text(
                  'Service providers will be notified about your job posting and will respond by bidding on your job. You\'ll receive notifications when artisans express interest.',
                  style: MyTypography.Regular.copyWith(
                    fontSize: 14,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Action button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      // Close dialog and navigate to home screen
                      Navigator.of(context).pop();
                      context.goNamed(RouteConstants.CLIENT_HOME_SCREEN);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: orangeColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Go to Home',
                      style: MyTypography.SemiBold.copyWith(fontSize: 16),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
