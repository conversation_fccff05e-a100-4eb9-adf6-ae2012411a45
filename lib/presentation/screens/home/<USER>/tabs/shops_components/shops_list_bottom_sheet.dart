import 'package:build_mate/presentation/components/helper_widgets/spacing_widgets.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/shop_details_sheet.dart';
import 'package:build_mate/presentation/view_models/user/shops_view_model.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/search_shop_textfield.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';

class ShopsListBottomSheet extends ConsumerWidget {
  const ShopsListBottomSheet({super.key});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(shopsViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            IconButton(
              icon: Icon(
                Icons.close,
                color: customColors.textPrimaryColor.withValues(alpha: 0.7),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        vSpace(16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: const SearchShopTextField(),
        ),
        Expanded(
          child:
              state.isLoading
                  ? ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: 6,
                    itemBuilder: (context, index) {
                      return Container(
                        height: 100,
                        margin: const EdgeInsets.symmetric(
                          vertical: 4,
                          horizontal: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow:
                              isDarkMode
                                  ? [
                                    BoxShadow(
                                      color: Colors.black.withAlpha(
                                        (0.3 * 255).round(),
                                      ),
                                      blurRadius: 6,
                                      offset: const Offset(0, 2),
                                    ),
                                  ]
                                  : [
                                    BoxShadow(
                                      color: Colors.grey.withAlpha(
                                        (0.06 * 255).round(),
                                      ),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                          border:
                              isDarkMode
                                  ? Border.all(
                                    color: customColors.textPrimaryColor
                                        .withValues(alpha: 0.1),
                                    width: 1,
                                  )
                                  : null,
                        ),
                        child: Row(
                          children: [
                            Shimmer.fromColors(
                              baseColor:
                                  isDarkMode
                                      ? customColors.surfaceVariant
                                      : Colors.grey[300]!,
                              highlightColor:
                                  isDarkMode
                                      ? customColors.surfaceVariant.withValues(
                                        alpha: 0.5,
                                      )
                                      : Colors.grey[100]!,
                              child: Container(
                                width: 90,
                                height: 90,
                                color:
                                    isDarkMode
                                        ? customColors.surfaceVariant
                                        : Colors.grey[300],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Shimmer.fromColors(
                                    baseColor:
                                        isDarkMode
                                            ? customColors.surfaceVariant
                                            : Colors.grey[300]!,
                                    highlightColor:
                                        isDarkMode
                                            ? customColors.surfaceVariant
                                                .withValues(alpha: 0.5)
                                            : Colors.grey[100]!,
                                    child: Container(
                                      width: 120,
                                      height: 16,
                                      color:
                                          isDarkMode
                                              ? customColors.surfaceVariant
                                              : Colors.grey[300],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Shimmer.fromColors(
                                    baseColor:
                                        isDarkMode
                                            ? customColors.surfaceVariant
                                            : Colors.grey[300]!,
                                    highlightColor:
                                        isDarkMode
                                            ? customColors.surfaceVariant
                                                .withValues(alpha: 0.5)
                                            : Colors.grey[100]!,
                                    child: Container(
                                      width: 80,
                                      height: 12,
                                      color:
                                          isDarkMode
                                              ? customColors.surfaceVariant
                                              : Colors.grey[300],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Shimmer.fromColors(
                              baseColor:
                                  isDarkMode
                                      ? customColors.surfaceVariant
                                      : Colors.grey[300]!,
                              highlightColor:
                                  isDarkMode
                                      ? customColors.surfaceVariant.withValues(
                                        alpha: 0.5,
                                      )
                                      : Colors.grey[100]!,
                              child: Container(
                                width: 40,
                                height: 16,
                                color:
                                    isDarkMode
                                        ? customColors.surfaceVariant
                                        : Colors.grey[300],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  )
                  : ListView.separated(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: state.nearbyShops.length,
                    separatorBuilder:
                        (_, __) => Divider(
                          height: 1,
                          color:
                              isDarkMode
                                  ? customColors.textPrimaryColor.withValues(
                                    alpha: 0.1,
                                  )
                                  : Colors.grey.withAlpha((0.15 * 255).round()),
                        ),
                    itemBuilder: (context, index) {
                      final shop = state.nearbyShops[index];
                      final imageUrl =
                          (shop.hardwareShop?.images != null &&
                                  shop.hardwareShop!.images!.isNotEmpty)
                              ? shop.hardwareShop!.images!.first
                              : null;
                      return GestureDetector(
                        onTap: () {
                          ref
                              .read(shopsViewModelProvider.notifier)
                              .selectShop(shop);
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder:
                                (_) => ShopDetailsSheet(() {
                                  Navigator.of(context).pop();
                                  ref
                                      .read(shopsViewModelProvider.notifier)
                                      .setShowdetailsVisibility(false);
                                  ref
                                      .read(shopsViewModelProvider.notifier)
                                      .selectShop(null);
                                }),
                          );
                        },
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.symmetric(
                            vertical: 4,
                            horizontal: 8,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).cardColor,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow:
                                isDarkMode
                                    ? [
                                      BoxShadow(
                                        color: Colors.black.withAlpha(
                                          (0.3 * 255).round(),
                                        ),
                                        blurRadius: 6,
                                        offset: const Offset(0, 2),
                                      ),
                                    ]
                                    : [
                                      BoxShadow(
                                        color: Colors.grey.withAlpha(
                                          (0.06 * 255).round(),
                                        ),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                            border:
                                isDarkMode
                                    ? Border.all(
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.1),
                                      width: 1,
                                    )
                                    : null,
                          ),
                          child: Row(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child:
                                    imageUrl != null
                                        ? Image.network(
                                          imageUrl,
                                          width: 90,
                                          height: 90,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Container(
                                                    width: 90,
                                                    height: 90,
                                                    color:
                                                        isDarkMode
                                                            ? customColors
                                                                .surfaceVariant
                                                            : Colors.grey[200],
                                                    child: Icon(
                                                      Icons.store,
                                                      color: customColors
                                                          .textPrimaryColor
                                                          .withValues(
                                                            alpha: 0.4,
                                                          ),
                                                    ),
                                                  ),
                                        )
                                        : Container(
                                          width: 90,
                                          height: 90,
                                          color:
                                              isDarkMode
                                                  ? customColors.surfaceVariant
                                                  : Colors.grey[200],
                                          child: Icon(
                                            Icons.store,
                                            color: customColors.textPrimaryColor
                                                .withValues(alpha: 0.4),
                                          ),
                                        ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      shop.hardwareShop?.name ?? '',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 16,
                                        color: customColors.textPrimaryColor,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      shop.name ?? '',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: customColors.textPrimaryColor
                                            .withValues(alpha: 0.6),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    shop.distanceKm != null
                                        ? '${shop.distanceKm!.toStringAsFixed(1)} km'
                                        : '',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14,
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.8),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
        ),
      ],
    );
  }
}
