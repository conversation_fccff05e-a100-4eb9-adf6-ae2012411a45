import 'package:build_mate/presentation/components/button/plain_button.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/screens/home/<USER>/bottom_nav_bar.dart';
import 'package:build_mate/presentation/view_models/user/client_profile_tab_view_model.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProfileTabView extends ConsumerWidget {
  const ProfileTabView({super.key});

  Widget _buildSettingsGroup({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title.toUpperCase(),
            style: MyTypography.SemiBold.copyWith(
              color: Colors.grey[600],
              fontSize: 13,
              letterSpacing: 0.5,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(children: children),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    Widget? trailing,
    VoidCallback? onTap,
    Color? iconColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: iconColor ?? orangeColor, size: 22),
      title: Text(
        title,
        style: MyTypography.Regular.copyWith(
          fontSize: 16,
          color: Colors.grey[800],
        ),
      ),
      trailing:
          trailing ??
          Icon(Icons.chevron_right, color: Colors.grey[400], size: 20),
      onTap: onTap,
    );
  }

  String _getThemeModeText(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  void _showThemeDialog(
    BuildContext context,
    ThemeModeNotifier themeModeNotifier,
    ThemeMode currentTheme,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Choose Theme',
            style: MyTypography.SemiBold.copyWith(fontSize: 18),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildThemeOption(
                context,
                'Light',
                Icons.light_mode,
                ThemeMode.light,
                currentTheme,
                themeModeNotifier,
              ),
              _buildThemeOption(
                context,
                'Dark',
                Icons.dark_mode,
                ThemeMode.dark,
                currentTheme,
                themeModeNotifier,
              ),
              _buildThemeOption(
                context,
                'System',
                Icons.settings_system_daydream,
                ThemeMode.system,
                currentTheme,
                themeModeNotifier,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    String title,
    IconData icon,
    ThemeMode themeMode,
    ThemeMode currentTheme,
    ThemeModeNotifier themeModeNotifier,
  ) {
    final isSelected = currentTheme == themeMode;
    return ListTile(
      leading: Icon(icon, color: isSelected ? orangeColor : Colors.grey[600]),
      title: Text(
        title,
        style: MyTypography.Regular.copyWith(
          fontSize: 16,
          color: isSelected ? orangeColor : Colors.grey[800],
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      trailing:
          isSelected ? Icon(Icons.check, color: orangeColor, size: 20) : null,
      onTap: () {
        themeModeNotifier.setThemeMode(themeMode);
        Navigator.of(context).pop();
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(clientProfileTabViewModelProvider);
    final notifier = ref.watch(clientProfileTabViewModelProvider.notifier);
    final themeMode = ref.watch(themeModeProvider);
    final themeModeNotifier = ref.read(themeModeProvider.notifier);
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200.0,
            floating: false,
            pinned: true,
            backgroundColor:
                themeMode == ThemeMode.light
                    ? darkBlueColor
                    : Theme.of(context).appBarTheme.backgroundColor,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color:
                    themeMode == ThemeMode.light
                        ? Colors.white
                        : Theme.of(context).appBarTheme.foregroundColor,
              ),
              onPressed: () => context.pop(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                color:
                    themeMode == ThemeMode.light
                        ? darkBlueColor
                        : Theme.of(context).appBarTheme.backgroundColor,
                child: Stack(
                  children: [
                    Positioned(
                      bottom: 20,
                      left: 20,
                      right: 20,
                      child: Row(
                        children: [
                          SizedBox(
                            width: 80,
                            height: 80,
                            child: ClipOval(
                              child: Image.network(
                                state.profileUrl,
                                fit: BoxFit.cover,
                                loadingBuilder: (
                                  context,
                                  child,
                                  loadingProgress,
                                ) {
                                  if (loadingProgress == null) return child;
                                  return Container(
                                    width: 80,
                                    height: 80,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      shape: BoxShape.circle,
                                    ),
                                    child: SizedBox(
                                      width: 32,
                                      height: 32,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 3,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              Colors.blue,
                                            ),
                                      ),
                                    ),
                                  );
                                },
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      color: Colors.grey[300],
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.person,
                                      color: Colors.grey[600],
                                      size: 40,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  state.username,
                                  style: MyTypography.SemiBold.copyWith(
                                    fontSize: 24,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                // Container(
                                //   padding: const EdgeInsets.symmetric(
                                //     horizontal: 8,
                                //     vertical: 4,
                                //   ),
                                //   decoration: BoxDecoration(
                                //     color: Colors.green.withAlpha(
                                //       51,
                                //     ), // 0.2 opacity
                                //     borderRadius: BorderRadius.circular(12),
                                //   ),
                                //   child: Text(
                                //     'Online',
                                //     style: MyTypography.Medium.copyWith(
                                //       fontSize: 12,
                                //       color: Colors.green,
                                //     ),
                                //   ),
                                // ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Column(
              children: [
                const SizedBox(height: 24),
                _buildSettingsGroup(
                  title: 'ACCOUNT',
                  children: [
                    _buildSettingsTile(
                      icon: Icons.verified_user,
                      title: 'Profile',
                      onTap: () {
                        context.pushNamed(
                          RouteConstants.CLIENT_SETTINGS_SCREEN,
                        );
                      },
                    ),
                    // Divider(height: 1, color: Colors.grey[200]),
                    // _buildSettingsTile(
                    //   icon: Icons.download_outlined,
                    //   title: 'Downloads',
                    //   onTap: () {},
                    // ),
                  ],
                ),
                _buildSettingsGroup(
                  title: 'PREFERENCES',
                  children: [
                    _buildSettingsTile(
                      icon: Icons.language,
                      title: 'Language',
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'English',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.chevron_right,
                            color: Colors.grey[400],
                            size: 20,
                          ),
                        ],
                      ),
                      onTap: () {},
                    ),
                    Divider(height: 1, color: Colors.grey[200]),
                    _buildSettingsTile(
                      icon: Icons.palette_outlined,
                      title: 'Theme',
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _getThemeModeText(themeMode),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.chevron_right,
                            color: Colors.grey[400],
                            size: 20,
                          ),
                        ],
                      ),
                      onTap:
                          () => _showThemeDialog(
                            context,
                            themeModeNotifier,
                            themeMode,
                          ),
                    ),
                    Divider(height: 1, color: Colors.grey[200]),
                    // _buildSettingsTile(
                    //   icon: Icons.wifi,
                    //   title: 'Only Download via Wi-Fi',
                    //   trailing: Switch(
                    //     value: true,
                    //     onChanged: (value) {},
                    //     activeColor: Colors.blue,
                    //   ),
                    // ),
                    Divider(height: 1, color: Colors.grey[200]),
                    // _buildSettingsTile(
                    //   icon: Icons.play_circle_outline,
                    //   title: 'Play in Background',
                    //   trailing: Switch(
                    //     value: false,
                    //     onChanged: (value) {},
                    //     activeColor: Colors.blue,
                    //   ),
                    // ),
                  ],
                ),

                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: PlainButton(
                    text: 'Logout',
                    style: MyTypography.Bold.copyWith(
                      fontSize: 16,
                      color: Colors.red,
                    ),
                    onPressed: () {
                      notifier.logout().then((_) {
                        if (!context.mounted) return;
                        // Set bottom nav bar index to zero after logout
                        final container = ProviderScope.containerOf(
                          context,
                          listen: false,
                        );
                        container
                            .read(homeNavigationIndexProvider.notifier)
                            .state = 0;
                        context.goNamed(RouteConstants.ROLE_SELECTION_SCREEN);
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
